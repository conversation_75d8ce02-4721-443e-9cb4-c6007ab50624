#!/usr/bin/env python3
"""
Real-time Speech-to-Text CLI Application

This application automatically detects the best audio input device,
captures audio in real-time, and displays transcribed speech as you talk.

Usage:
    python realtime_speech_to_text.py [options]

Example:
    python realtime_speech_to_text.py --model base --language en
"""

import os
import sys
import argparse
import threading
import time
import queue
import numpy as np
import pyaudio
import whisper
from typing import Optional, List, Dict, Any, Tuple
import warnings
from pathlib import Path
from datetime import datetime
import collections

# Suppress some warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)


def find_local_model(model_name: str) -> Optional[str]:
    """
    Find a local model file in common locations.

    Args:
        model_name: Name of the model to find

    Returns:
        Path to the model file if found, None otherwise
    """
    # Possible model filenames
    model_filenames = [
        f"{model_name}.pt",
        f"{model_name}-v3.pt" if model_name == "large" else f"{model_name}.pt"
    ]

    # Search locations (in order of preference)
    search_paths = [
        # Local models directory (for bundled distributions)
        Path("./models"),
        Path("./whisper_models"),

        # Relative to executable (for PyInstaller)
        Path(sys.executable).parent / "models",
        Path(sys.executable).parent / "whisper_models",

        # Standard cache locations
        Path.home() / ".cache" / "whisper",
        Path(os.environ.get("APPDATA", "")) / "whisper" if os.name == "nt" else Path.home() / ".cache" / "whisper",
    ]

    for search_path in search_paths:
        if search_path.exists():
            for filename in model_filenames:
                model_path = search_path / filename
                if model_path.exists():
                    print(f"📁 Found local model: {model_path}")
                    return str(model_path)

    return None


def load_whisper_model(model_name: str):
    """
    Load a Whisper model, preferring local files over downloads.

    Args:
        model_name: Name of the model to load

    Returns:
        Loaded Whisper model
    """
    # Try to find local model first
    local_model_path = find_local_model(model_name)

    if local_model_path:
        try:
            print(f"🔄 Loading local model from: {local_model_path}")
            return whisper.load_model(local_model_path)
        except Exception as e:
            print(f"⚠️  Failed to load local model: {e}")
            print("🔄 Falling back to download...")

    # Fall back to standard download
    print(f"🔄 Downloading model '{model_name}'...")
    return whisper.load_model(model_name)


class VoiceActivityDetector:
    """Voice Activity Detection using energy-based approach."""

    def __init__(self, sample_rate: int = 16000, frame_duration_ms: int = 30):
        """
        Initialize Voice Activity Detector.

        Args:
            sample_rate: Audio sample rate in Hz
            frame_duration_ms: Frame duration in milliseconds
        """
        self.sample_rate = sample_rate
        self.frame_duration_ms = frame_duration_ms
        self.frame_size = int(sample_rate * frame_duration_ms / 1000)

        # VAD parameters (more conservative settings)
        self.energy_threshold = 0.002  # Much lower threshold based on observed levels
        self.silence_duration_ms = 800  # Longer silence before ending (was 500ms)
        self.speech_pad_ms = 300  # More padding for better capture (was 200ms)
        self.min_speech_frames = 10  # Minimum frames to consider as speech
        self.speech_multiplier = 3.0  # Speech should be this many times louder than background

        # State tracking
        self.silence_frames = int(self.silence_duration_ms / frame_duration_ms)
        self.pad_frames = int(self.speech_pad_ms / frame_duration_ms)
        self.consecutive_silence = 0
        self.is_speaking = False

        # Separate buffers for different purposes
        self.pre_speech_buffer = collections.deque(maxlen=self.pad_frames)  # Pre-speech padding
        self.speech_frames = []  # Main speech storage (no size limit)
        self.current_speech_length = 0

        # Energy history for adaptive threshold
        self.energy_history = collections.deque(maxlen=100)
        self.adaptive_threshold = True

        # Debug counters
        self.frame_count = 0
        self.debug_interval = 100  # Print debug info every N frames
        self.debug_enabled = False  # Will be set by main class

    def calculate_energy(self, audio_frame: np.ndarray) -> float:
        """Calculate RMS energy of audio frame."""
        if len(audio_frame) == 0:
            return 0.0
        return np.sqrt(np.mean(audio_frame ** 2))

    def update_adaptive_threshold(self, energy: float):
        """Update adaptive energy threshold based on background noise."""
        self.energy_history.append(energy)
        if len(self.energy_history) >= 50:  # Need enough samples
            # Set threshold based on background noise level
            mean_energy = np.mean(self.energy_history)
            std_energy = np.std(self.energy_history)
            # Threshold should be background + some margin, but not too high
            adaptive_threshold = mean_energy + (std_energy * self.speech_multiplier)
            self.energy_threshold = max(0.001, min(0.01, adaptive_threshold))

    def process_frame(self, audio_frame: np.ndarray) -> Tuple[bool, Optional[np.ndarray]]:
        """
        Process audio frame and detect voice activity.

        Args:
            audio_frame: Audio frame to process

        Returns:
            Tuple of (is_voice_detected, speech_segment_if_complete)
        """
        # Process the frame in smaller chunks if it's larger than expected
        frame_results = []

        # Split large frames into smaller VAD-sized chunks
        start_idx = 0
        while start_idx < len(audio_frame):
            end_idx = min(start_idx + self.frame_size, len(audio_frame))
            chunk = audio_frame[start_idx:end_idx]

            # Pad chunk to expected size if needed
            if len(chunk) < self.frame_size:
                chunk = np.pad(chunk, (0, self.frame_size - len(chunk)))

            result = self._process_single_frame(chunk)
            frame_results.append(result)
            start_idx = end_idx

        # Return the result from the last chunk (most recent)
        return frame_results[-1] if frame_results else (False, None)

    def _process_single_frame(self, audio_frame: np.ndarray) -> Tuple[bool, Optional[np.ndarray]]:
        """Process a single frame of the expected size."""
        # Calculate energy
        energy = self.calculate_energy(audio_frame)

        # Update adaptive threshold
        if self.adaptive_threshold:
            self.update_adaptive_threshold(energy)

        # Debug output every N frames (if enabled)
        self.frame_count += 1
        if self.debug_enabled and self.frame_count % self.debug_interval == 0:
            print(f"🔍 Debug: Frame {self.frame_count}, Energy: {energy:.6f}, Threshold: {self.energy_threshold:.6f}, Speaking: {self.is_speaking}")

        # Voice activity detection
        is_voice = energy > self.energy_threshold

        # Always add to pre-speech buffer for padding
        self.pre_speech_buffer.append(audio_frame)

        if is_voice:
            if not self.is_speaking:
                # Start of speech detected - include pre-speech padding
                self.is_speaking = True
                self.speech_frames = list(self.pre_speech_buffer)  # Add padding
                self.current_speech_length = len(self.speech_frames)
                print(f"🎤 Voice detected - recording... (threshold: {self.energy_threshold:.4f}, energy: {energy:.4f})")
            else:
                # Continue recording speech
                self.speech_frames.append(audio_frame)
                self.current_speech_length += 1

            self.consecutive_silence = 0
        else:
            if self.is_speaking:
                # Still in speech mode but current frame is silent
                self.speech_frames.append(audio_frame)  # Include silence in speech
                self.current_speech_length += 1
                self.consecutive_silence += 1

                if self.consecutive_silence >= self.silence_frames:
                    # End of speech detected
                    self.is_speaking = False
                    self.consecutive_silence = 0

                    # Calculate duration
                    total_samples = self.current_speech_length * self.frame_size
                    duration = total_samples / self.sample_rate
                    print(f"🔇 Voice ended - processing... ({duration:.1f}s, {self.current_speech_length} frames)")

                    # Check minimum speech length
                    if self.current_speech_length >= self.min_speech_frames:
                        # Return complete speech segment
                        speech_segment = np.concatenate(self.speech_frames)

                        # Reset for next speech segment
                        self.speech_frames = []
                        self.current_speech_length = 0

                        return False, speech_segment
                    else:
                        print(f"⏭️  Speech too short ({self.current_speech_length} frames < {self.min_speech_frames}), discarding")
                        # Reset without returning segment
                        self.speech_frames = []
                        self.current_speech_length = 0

        return is_voice, None


class AudioDeviceManager:
    """Manages audio input devices and selects the best one."""
    
    def __init__(self):
        self.audio = pyaudio.PyAudio()
    
    def get_audio_devices(self) -> List[Dict[str, Any]]:
        """Get all available audio input devices."""
        devices = []
        device_count = self.audio.get_device_count()
        
        for i in range(device_count):
            try:
                device_info = self.audio.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:  # Input device
                    devices.append({
                        'index': i,
                        'name': device_info['name'],
                        'channels': device_info['maxInputChannels'],
                        'sample_rate': int(device_info['defaultSampleRate']),
                        'host_api': self.audio.get_host_api_info_by_index(device_info['hostApi'])['name']
                    })
            except Exception:
                continue
        
        return devices
    
    def select_best_device(self) -> Optional[Dict[str, Any]]:
        """Select the best available audio input device."""
        devices = self.get_audio_devices()
        
        if not devices:
            return None
        
        # Prioritize devices with higher sample rates and more channels
        # Also prefer certain host APIs (DirectSound, WASAPI on Windows)
        def device_score(device):
            score = 0
            score += device['sample_rate'] / 1000  # Higher sample rate is better
            score += device['channels'] * 10  # More channels is better
            
            # Prefer certain host APIs
            preferred_apis = ['WASAPI', 'DirectSound', 'Core Audio', 'ALSA']
            if device['host_api'] in preferred_apis:
                score += 100
            
            # Prefer devices with "microphone" or "mic" in the name
            name_lower = device['name'].lower()
            if 'microphone' in name_lower or 'mic' in name_lower:
                score += 50
            
            return score
        
        best_device = max(devices, key=device_score)
        return best_device
    
    def list_devices(self):
        """Print all available audio input devices."""
        devices = self.get_audio_devices()
        
        print("\n📱 Available Audio Input Devices:")
        print("-" * 60)
        
        for device in devices:
            print(f"  [{device['index']}] {device['name']}")
            print(f"      Channels: {device['channels']}, Sample Rate: {device['sample_rate']} Hz")
            print(f"      Host API: {device['host_api']}")
            print()
    
    def cleanup(self):
        """Clean up PyAudio resources."""
        self.audio.terminate()


class RealTimeSpeechToText:
    """Real-time speech-to-text converter using OpenAI Whisper with Voice Activity Detection."""

    def __init__(self, model_name: str = 'base', language: Optional[str] = None,
                 output_file: Optional[str] = None, debug_vad: bool = False):
        """
        Initialize the real-time speech-to-text converter.

        Args:
            model_name: Whisper model to use
            language: Language code (e.g., 'en', 'es', 'fr')
            output_file: Path to output text file (optional)
        """
        print(f"🔄 Loading Whisper model '{model_name}'...")
        self.model = load_whisper_model(model_name)
        self.language = language
        print(f"✅ Model '{model_name}' loaded successfully!")

        # Audio settings
        self.sample_rate = 16000  # Whisper expects 16kHz
        self.frame_duration_ms = 30  # VAD frame duration
        self.min_speech_duration = 0.3  # Reduced minimum speech duration (was 0.5s)

        # Voice Activity Detection
        self.vad = VoiceActivityDetector(self.sample_rate, self.frame_duration_ms)
        self.vad.debug_enabled = debug_vad

        # Threading and queues for parallel processing
        self.raw_audio_queue = queue.Queue()  # Raw audio frames
        self.speech_queue = queue.Queue()     # Detected speech segments
        self.transcription_queue = queue.Queue()  # Transcribed text
        self.is_running = False
        self.device_manager = AudioDeviceManager()

        # Output file handling
        self.output_file = output_file
        self.output_lock = threading.Lock()

        # Statistics
        self.segments_processed = 0
        self.start_time = None
        
    def audio_callback(self, in_data, frame_count, time_info, status):
        """Callback function for audio input."""
        if status:
            print(f"⚠️  Audio status: {status}")

        # Convert bytes to numpy array
        audio_data = np.frombuffer(in_data, dtype=np.float32)
        self.raw_audio_queue.put(audio_data)

        return (in_data, pyaudio.paContinue)
    
    def voice_detection_thread(self):
        """Thread for voice activity detection and speech segmentation."""
        print("🔍 Voice detection thread started")

        while self.is_running:
            try:
                # Get raw audio data
                audio_chunk = self.raw_audio_queue.get(timeout=0.1)

                # Process with VAD
                is_voice, speech_segment = self.vad.process_frame(audio_chunk)

                # If we have a complete speech segment, queue it for transcription
                if speech_segment is not None:
                    # Check minimum duration
                    duration = len(speech_segment) / self.sample_rate
                    print(f"🔍 Speech segment detected: {duration:.2f}s ({len(speech_segment)} samples)")
                    if duration >= self.min_speech_duration:
                        self.speech_queue.put(speech_segment)
                        print(f"📝 Speech segment queued for transcription ({duration:.2f}s)")
                    else:
                        print(f"⏭️  Speech segment too short ({duration:.2f}s < {self.min_speech_duration}s), skipping")

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Error in voice detection thread: {e}")

    def transcription_thread(self):
        """Thread for transcribing detected speech segments."""
        print("🎯 Transcription thread started")

        while self.is_running:
            try:
                # Get speech segment to transcribe
                speech_segment = self.speech_queue.get(timeout=0.1)

                # Transcribe the speech segment
                text = self.transcribe_speech_segment(speech_segment)

                if text:
                    # Queue transcribed text for output
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    self.transcription_queue.put((timestamp, text))
                    self.segments_processed += 1

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Error in transcription thread: {e}")

    def transcribe_speech_segment(self, audio_data: np.ndarray) -> str:
        """Transcribe a speech segment using Whisper."""
        try:
            # Ensure minimum length for Whisper
            min_samples = int(0.1 * self.sample_rate)  # 0.1 second minimum
            if len(audio_data) < min_samples:
                return ""

            # Transcribe the audio segment
            result = self.model.transcribe(
                audio_data,
                language=self.language,
                verbose=False,
                fp16=False  # Use fp32 for better compatibility
            )

            text = result['text'].strip()
            return text if text else ""

        except Exception as e:
            print(f"❌ Error transcribing speech: {e}")
            return ""

    def output_thread(self):
        """Thread for handling transcription output to console and file."""
        print("📄 Output thread started")

        while self.is_running:
            try:
                # Get transcribed text
                timestamp, text = self.transcription_queue.get(timeout=0.1)

                # Display to console
                print(f"🎤 [{timestamp}] {text}")

                # Write to file if specified
                if self.output_file:
                    self.write_to_file(timestamp, text)

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Error in output thread: {e}")

    def write_to_file(self, timestamp: str, text: str):
        """Write transcribed text to output file."""
        try:
            with self.output_lock:
                with open(self.output_file, 'a', encoding='utf-8') as f:
                    f.write(f"[{timestamp}] {text}\n")
                    f.flush()  # Ensure immediate write
        except Exception as e:
            print(f"❌ Error writing to file: {e}")

    def print_statistics(self):
        """Print processing statistics."""
        if self.start_time:
            elapsed = time.time() - self.start_time
            print(f"\n📊 Session Statistics:")
            print(f"   Duration: {elapsed:.1f} seconds")
            print(f"   Segments processed: {self.segments_processed}")
            if self.segments_processed > 0:
                print(f"   Average time per segment: {elapsed/self.segments_processed:.1f}s")
            if self.output_file:
                print(f"   Output saved to: {self.output_file}")
    
    def start_listening(self):
        """Start real-time audio capture and processing with parallel threads."""
        # Select the best audio device
        device = self.device_manager.select_best_device()

        if not device:
            print("❌ No audio input devices found!")
            return False

        print(f"🎯 Selected audio device: {device['name']}")
        print(f"   Sample Rate: {device['sample_rate']} Hz, Channels: {device['channels']}")
        if self.output_file:
            print(f"📄 Output file: {self.output_file}")
        print()

        try:
            # Start audio stream
            stream = self.device_manager.audio.open(
                format=pyaudio.paFloat32,
                channels=1,  # Mono
                rate=self.sample_rate,
                input=True,
                input_device_index=device['index'],
                frames_per_buffer=1024,
                stream_callback=self.audio_callback
            )

            # Start all processing threads
            self.is_running = True
            self.start_time = time.time()

            # Voice detection thread (fast, real-time)
            vad_thread = threading.Thread(target=self.voice_detection_thread)
            vad_thread.daemon = True
            vad_thread.start()

            # Transcription thread (slower, processes speech segments)
            transcription_thread = threading.Thread(target=self.transcription_thread)
            transcription_thread.daemon = True
            transcription_thread.start()

            # Output thread (handles console and file output)
            output_thread = threading.Thread(target=self.output_thread)
            output_thread.daemon = True
            output_thread.start()

            # Start the audio stream
            stream.start_stream()

            print("🎙️  Real-time speech recognition started with Voice Activity Detection!")
            print("💬 Start speaking... (Press Ctrl+C to stop)")
            print("🔍 Voice detection is active - only speech will be processed")
            print("-" * 70)

            # Keep the main thread alive
            try:
                while stream.is_active():
                    time.sleep(0.1)
            except KeyboardInterrupt:
                print("\n\n🛑 Stopping speech recognition...")

            # Cleanup
            self.is_running = False
            stream.stop_stream()
            stream.close()

            # Print final statistics
            self.print_statistics()

            return True

        except Exception as e:
            print(f"❌ Error starting audio stream: {e}")
            return False
    
    def cleanup(self):
        """Clean up resources."""
        self.is_running = False
        self.device_manager.cleanup()


def main():
    """Main function for the real-time speech-to-text CLI."""
    parser = argparse.ArgumentParser(
        description="Real-time Speech-to-Text using OpenAI Whisper",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python realtime_speech_to_text.py
  python realtime_speech_to_text.py --model small --language en
  python realtime_speech_to_text.py --output transcription.txt
  python realtime_speech_to_text.py --model base --language en --output speech.txt
  python realtime_speech_to_text.py --list-devices
        """
    )
    
    parser.add_argument(
        '--model', '-m',
        type=str,
        default='base',
        choices=['tiny', 'base', 'small', 'medium', 'large'],
        help='Whisper model to use (default: base)'
    )
    
    parser.add_argument(
        '--language', '-l',
        type=str,
        help='Language code (e.g., en, es, fr). Auto-detect if not specified'
    )
    
    parser.add_argument(
        '--list-devices',
        action='store_true',
        help='List available audio input devices and exit'
    )

    parser.add_argument(
        '--output', '-o',
        type=str,
        help='Output text file to save transcriptions (optional)'
    )

    parser.add_argument(
        '--debug-vad',
        action='store_true',
        help='Enable Voice Activity Detection debug output'
    )

    args = parser.parse_args()
    
    # Initialize device manager for listing devices
    if args.list_devices:
        device_manager = AudioDeviceManager()
        device_manager.list_devices()
        device_manager.cleanup()
        return
    
    print("🎯 Real-time Speech-to-Text CLI")
    print("=" * 40)
    
    try:
        # Initialize the speech-to-text converter
        converter = RealTimeSpeechToText(
            model_name=args.model,
            language=args.language,
            output_file=args.output,
            debug_vad=args.debug_vad
        )
        
        # Start listening
        success = converter.start_listening()
        
        # Cleanup
        converter.cleanup()
        
        if success:
            print("✅ Session completed successfully!")
        else:
            print("❌ Session failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Application interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
