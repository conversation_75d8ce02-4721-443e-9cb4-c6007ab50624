#!/usr/bin/env python3
"""
Real-time Speech-to-Text CLI Application

This application automatically detects the best audio input device,
captures audio in real-time, and displays transcribed speech as you talk.

Usage:
    python realtime_speech_to_text.py [options]

Example:
    python realtime_speech_to_text.py --model base --language en
"""

import os
import sys
import argparse
import threading
import time
import queue
import numpy as np
import pyaudio
import whisper
from typing import Optional, List, Dict, Any
import warnings
from pathlib import Path

# Suppress some warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)


def find_local_model(model_name: str) -> Optional[str]:
    """
    Find a local model file in common locations.

    Args:
        model_name: Name of the model to find

    Returns:
        Path to the model file if found, None otherwise
    """
    # Possible model filenames
    model_filenames = [
        f"{model_name}.pt",
        f"{model_name}-v3.pt" if model_name == "large" else f"{model_name}.pt"
    ]

    # Search locations (in order of preference)
    search_paths = [
        # Local models directory (for bundled distributions)
        Path("./models"),
        Path("./whisper_models"),

        # Relative to executable (for PyInstaller)
        Path(sys.executable).parent / "models",
        Path(sys.executable).parent / "whisper_models",

        # Standard cache locations
        Path.home() / ".cache" / "whisper",
        Path(os.environ.get("APPDATA", "")) / "whisper" if os.name == "nt" else Path.home() / ".cache" / "whisper",
    ]

    for search_path in search_paths:
        if search_path.exists():
            for filename in model_filenames:
                model_path = search_path / filename
                if model_path.exists():
                    print(f"📁 Found local model: {model_path}")
                    return str(model_path)

    return None


def load_whisper_model(model_name: str):
    """
    Load a Whisper model, preferring local files over downloads.

    Args:
        model_name: Name of the model to load

    Returns:
        Loaded Whisper model
    """
    # Try to find local model first
    local_model_path = find_local_model(model_name)

    if local_model_path:
        try:
            print(f"🔄 Loading local model from: {local_model_path}")
            return whisper.load_model(local_model_path)
        except Exception as e:
            print(f"⚠️  Failed to load local model: {e}")
            print("🔄 Falling back to download...")

    # Fall back to standard download
    print(f"🔄 Downloading model '{model_name}'...")
    return whisper.load_model(model_name)


class AudioDeviceManager:
    """Manages audio input devices and selects the best one."""
    
    def __init__(self):
        self.audio = pyaudio.PyAudio()
    
    def get_audio_devices(self) -> List[Dict[str, Any]]:
        """Get all available audio input devices."""
        devices = []
        device_count = self.audio.get_device_count()
        
        for i in range(device_count):
            try:
                device_info = self.audio.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:  # Input device
                    devices.append({
                        'index': i,
                        'name': device_info['name'],
                        'channels': device_info['maxInputChannels'],
                        'sample_rate': int(device_info['defaultSampleRate']),
                        'host_api': self.audio.get_host_api_info_by_index(device_info['hostApi'])['name']
                    })
            except Exception:
                continue
        
        return devices
    
    def select_best_device(self) -> Optional[Dict[str, Any]]:
        """Select the best available audio input device."""
        devices = self.get_audio_devices()
        
        if not devices:
            return None
        
        # Prioritize devices with higher sample rates and more channels
        # Also prefer certain host APIs (DirectSound, WASAPI on Windows)
        def device_score(device):
            score = 0
            score += device['sample_rate'] / 1000  # Higher sample rate is better
            score += device['channels'] * 10  # More channels is better
            
            # Prefer certain host APIs
            preferred_apis = ['WASAPI', 'DirectSound', 'Core Audio', 'ALSA']
            if device['host_api'] in preferred_apis:
                score += 100
            
            # Prefer devices with "microphone" or "mic" in the name
            name_lower = device['name'].lower()
            if 'microphone' in name_lower or 'mic' in name_lower:
                score += 50
            
            return score
        
        best_device = max(devices, key=device_score)
        return best_device
    
    def list_devices(self):
        """Print all available audio input devices."""
        devices = self.get_audio_devices()
        
        print("\n📱 Available Audio Input Devices:")
        print("-" * 60)
        
        for device in devices:
            print(f"  [{device['index']}] {device['name']}")
            print(f"      Channels: {device['channels']}, Sample Rate: {device['sample_rate']} Hz")
            print(f"      Host API: {device['host_api']}")
            print()
    
    def cleanup(self):
        """Clean up PyAudio resources."""
        self.audio.terminate()


class RealTimeSpeechToText:
    """Real-time speech-to-text converter using OpenAI Whisper."""
    
    def __init__(self, model_name: str = 'base', language: Optional[str] = None):
        """
        Initialize the real-time speech-to-text converter.
        
        Args:
            model_name: Whisper model to use
            language: Language code (e.g., 'en', 'es', 'fr')
        """
        print(f"🔄 Loading Whisper model '{model_name}'...")
        self.model = load_whisper_model(model_name)
        self.language = language
        print(f"✅ Model '{model_name}' loaded successfully!")
        
        # Audio settings
        self.sample_rate = 16000  # Whisper expects 16kHz
        self.chunk_duration = 3  # Process audio in 3-second chunks
        self.chunk_size = self.sample_rate * self.chunk_duration
        
        # Threading and queue for audio processing
        self.audio_queue = queue.Queue()
        self.is_running = False
        self.device_manager = AudioDeviceManager()
        
    def audio_callback(self, in_data, frame_count, time_info, status):
        """Callback function for audio input."""
        if status:
            print(f"⚠️  Audio status: {status}")
        
        # Convert bytes to numpy array
        audio_data = np.frombuffer(in_data, dtype=np.float32)
        self.audio_queue.put(audio_data)
        
        return (in_data, pyaudio.paContinue)
    
    def process_audio_chunk(self, audio_data: np.ndarray) -> str:
        """Process a chunk of audio data and return transcribed text."""
        try:
            # Ensure audio is the right length and format
            if len(audio_data) < self.chunk_size:
                # Pad with zeros if too short
                audio_data = np.pad(audio_data, (0, self.chunk_size - len(audio_data)))
            elif len(audio_data) > self.chunk_size:
                # Truncate if too long
                audio_data = audio_data[:self.chunk_size]
            
            # Transcribe the audio chunk
            result = self.model.transcribe(
                audio_data,
                language=self.language,
                verbose=False,
                fp16=False  # Use fp32 for better compatibility
            )
            
            text = result['text'].strip()
            return text if text else ""
            
        except Exception as e:
            print(f"❌ Error processing audio: {e}")
            return ""
    
    def audio_processing_thread(self):
        """Thread function for processing audio chunks."""
        audio_buffer = np.array([], dtype=np.float32)
        
        while self.is_running:
            try:
                # Get audio data from queue (with timeout)
                audio_chunk = self.audio_queue.get(timeout=0.1)
                audio_buffer = np.concatenate([audio_buffer, audio_chunk])
                
                # Process when we have enough audio data
                if len(audio_buffer) >= self.chunk_size:
                    # Extract chunk for processing
                    chunk_to_process = audio_buffer[:self.chunk_size]
                    audio_buffer = audio_buffer[self.chunk_size//2:]  # 50% overlap
                    
                    # Process the chunk
                    text = self.process_audio_chunk(chunk_to_process)
                    
                    # Display the result
                    if text:
                        print(f"🎤 {text}")
                        
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Error in audio processing thread: {e}")
    
    def start_listening(self):
        """Start real-time audio capture and processing."""
        # Select the best audio device
        device = self.device_manager.select_best_device()
        
        if not device:
            print("❌ No audio input devices found!")
            return False
        
        print(f"🎯 Selected audio device: {device['name']}")
        print(f"   Sample Rate: {device['sample_rate']} Hz, Channels: {device['channels']}")
        print()
        
        try:
            # Start audio stream
            stream = self.device_manager.audio.open(
                format=pyaudio.paFloat32,
                channels=1,  # Mono
                rate=self.sample_rate,
                input=True,
                input_device_index=device['index'],
                frames_per_buffer=1024,
                stream_callback=self.audio_callback
            )
            
            # Start processing thread
            self.is_running = True
            processing_thread = threading.Thread(target=self.audio_processing_thread)
            processing_thread.daemon = True
            processing_thread.start()
            
            # Start the stream
            stream.start_stream()
            
            print("🎙️  Real-time speech recognition started!")
            print("💬 Start speaking... (Press Ctrl+C to stop)")
            print("-" * 60)
            
            # Keep the main thread alive
            try:
                while stream.is_active():
                    time.sleep(0.1)
            except KeyboardInterrupt:
                print("\n\n🛑 Stopping speech recognition...")
            
            # Cleanup
            self.is_running = False
            stream.stop_stream()
            stream.close()
            
            return True
            
        except Exception as e:
            print(f"❌ Error starting audio stream: {e}")
            return False
    
    def cleanup(self):
        """Clean up resources."""
        self.is_running = False
        self.device_manager.cleanup()


def main():
    """Main function for the real-time speech-to-text CLI."""
    parser = argparse.ArgumentParser(
        description="Real-time Speech-to-Text using OpenAI Whisper",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python realtime_speech_to_text.py
  python realtime_speech_to_text.py --model small --language en
  python realtime_speech_to_text.py --list-devices
        """
    )
    
    parser.add_argument(
        '--model', '-m',
        type=str,
        default='base',
        choices=['tiny', 'base', 'small', 'medium', 'large'],
        help='Whisper model to use (default: base)'
    )
    
    parser.add_argument(
        '--language', '-l',
        type=str,
        help='Language code (e.g., en, es, fr). Auto-detect if not specified'
    )
    
    parser.add_argument(
        '--list-devices',
        action='store_true',
        help='List available audio input devices and exit'
    )
    
    args = parser.parse_args()
    
    # Initialize device manager for listing devices
    if args.list_devices:
        device_manager = AudioDeviceManager()
        device_manager.list_devices()
        device_manager.cleanup()
        return
    
    print("🎯 Real-time Speech-to-Text CLI")
    print("=" * 40)
    
    try:
        # Initialize the speech-to-text converter
        converter = RealTimeSpeechToText(
            model_name=args.model,
            language=args.language
        )
        
        # Start listening
        success = converter.start_listening()
        
        # Cleanup
        converter.cleanup()
        
        if success:
            print("✅ Session completed successfully!")
        else:
            print("❌ Session failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Application interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
